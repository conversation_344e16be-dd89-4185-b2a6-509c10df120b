# 微服务项目 - 技术架构大纲

## 项目整体结构
```
project-name/
├── pom.xml (父POM)
├── README.md
└── 模块结构/
    ├── xxx-client/         # 接口层
    ├── xxx-application/    # 应用层
    ├── xxx-domain/         # 领域层
    ├── xxx-infrastructure/ # 基础设施层
    └── xxx-adapter/        # 适配层
```

## 详细技术架构大纲

### 1. xxx-client（接口层）
```
xxx-client/
├── pom.xml
└── src/main/java/com/company/project/client/
    ├── constant/                # 常量定义
    │   ├── Constants.java
    │   └── TypeEnum.java
    ├── enums/                   # 枚举定义
    ├── service/                 # 服务接口定义
    │   ├── DataProcessService.java
    │   ├── BusinessService.java
    │   ├── HistoryService.java
    │   └── ... (其他服务接口)
    └── model/                   # 数据传输对象
        ├── command/             # 命令对象
        ├── common/              # 通用对象
        ├── entity/              # 实体对象
        │   ├── dto/             # DTO对象
        │   ├── rpc/             # RPC对象
        │   └── vo/              # VO对象
        ├── query/               # 查询对象
        ├── remote/              # 远程调用对象
        ├── req/                 # 请求对象
        ├── resp/                # 响应对象
        └── rpc/                 # RPC对象
```

### 2. xxx-application（应用层）
```
xxx-application/
├── pom.xml
└── src/main/java/com/company/project/application/
    ├── consumer/               # 事件消费者
    │   └── EventConsumer.java
    ├── convertor/              # 数据转换器
    ├── job/                    # 定时任务
    │   └── CacheLoadJob.java
    └── service/                # 应用服务实现
        ├── DataProcessServiceImpl.java
        ├── BusinessServiceImpl.java
        ├── HistoryServiceImpl.java
        └── ... (其他服务实现)
```

### 3. xxx-domain（领域层）
```
xxx-domain/
├── pom.xml
└── src/main/java/com/company/project/domain/
    ├── covertor/               # 领域转换器
    ├── extractor/              # 业务提取器
    │   ├── BusinessCollection.java
    │   └── BusinessExtractor.java
    ├── gateway/                # 领域网关
    │   ├── config/             # 配置接口
    │   ├── manager/            # 领域管理器
    │   ├── message/            # 消息服务接口
    │   ├── repository/         # 仓储接口
    │   ├── rpc/                # 远程调用接口
    │   └── utils/              # 工具类接口
    ├── handler/                # 处理器
    ├── service/                # 领域服务
    └── utils/                  # 工具类
```

### 4. xxx-infrastructure（基础设施层）
```
xxx-infrastructure/
├── pom.xml
└── src/main/java/com/company/project/infrastructure/
    ├── convertor/              # 基础设施转换器
    ├── dao/                    # 数据访问层
    │   ├── entity/             # 数据库实体
    │   ├── handler/            # 类型处理器
    │   └── mapper/             # MyBatis映射器
    ├── gateway/                # 网关实现
    │   ├── config/             # 配置实现
    │   ├── manager/            # 管理器实现
    │   ├── message/            # 消息服务实现
    │   ├── repository/         # 仓储实现
    │   ├── rpc/                # 远程调用实现
    │   └── utils/              # 工具类实现
    └── model/                  # 基础设施模型
```

### 5. xxx-adapter（适配层）
```
xxx-adapter/
├── pom.xml
└── src/main/java/com/company/project/adapter/
    ├── convertor/              # 适配器转换器
    │   └── MiddlewareConvertor.java
    └── handler/                # 事件处理器
        ├── ApiEventHandler.java
        └── BusinessEventHandler.java
```

### 项目分层结构说明
#### adapter 层（适配层）
- 负责与外部系统的交互适配，包括事件监听、消息消费等。
- 处理外部系统的协议转换，确保外部通信与内部领域模型的隔离。
- pom文件中**只能依赖**：本域domain、本域client、外域client、第三方依赖包。

#### application 层（应用层）
- 负责编排领域服务，组合多个领域对象完成业务流程。
- 实现client层定义的接口，消费本域的事件。
- 不包含业务规则，主要负责任务分派和流程编排。
- pom文件中**只能依赖**：本域domain、本域client。

#### client 层（接口层）
- 定义领域模型对外暴露的接口与数据传输对象（DTO）。
- 包含命令（Command）、查询（Query）、事件（Event）等接口定义。
- pom文件**禁止引入**非本域的其他依赖。

#### domain 层（领域层）
- 包含领域模型、领域服务、领域事件等核心业务逻辑。
- 通过领域对象和领域服务封装业务规则。
- 在gateway目录下定义仓储接口（Repository），子目录根据接口功能自行定义。
  - 示例：`gateway/repository/`存放数据库访问接口
  - 示例：`gateway/rpc/`存放远程调用接口
  - 示例：`gateway/message/`存放消息服务接口
  - 示例：`gateway/config/`存放配置服务接口
- 所有涉及到网络请求的操作（如MySQL、Redis、Apollo、RPC等），**必须在domain层的gateway目录下定义接口**，在infrastructure层实现。
- pom文件中**只依赖**本域client、本域infrastructure，**禁止依赖其他域的client**。

## 领域对象规范
### Command、Query、Event
- Command（命令）：表示对系统状态的修改意图，通常对应写操作。
- Query（查询）：表示对系统状态的查询意图，通常对应读操作。
- Event（事件）：表示系统中已发生的事实，用于领域事件的发布与订阅。

### DTO规范
- req、resp等传输对象定义在client的service包下。
- **只能在application层中使用**，禁止穿透到domain层。
- 业务写操作，应转换为command对象，通过domain service处理。
- 业务读操作，应转换为query对象，通过query service处理。

## Event事件处理规范
1. 领域事件（DomainEvent）：
   - 表达领域中发生的事实。
   - 事件实体类统一在client层定义。
   - 事件的发布在domain层处理。
   - 事件的处理通过Event Handler实现。

2. 必须异步的场景：
   - 在Application层，Spring Bean方法上标记@EventHandler注解。

3. 跨域异步场景：
   - 在adapter层，使用@EventHandler注解监听外域事件。

4. **Event非特殊情况不用做逻辑处理。**

### EventHandler使用约束
1. adapter层：
   - **只处理非本工程发出的外部事件**
   - 主要用于跨系统、跨领域的事件监听
   - 负责外部事件到内部命令的转换
   - 示例：监听其他系统的MQ消息、外部服务的事件通知等

2. application层：
   - **只处理本工程发出的内部事件**
   - 用于处理同一领域内的异步操作
   - 确保领域内的数据一致性
   - 示例：处理本系统的状态变更事件、数据同步事件等

### Event命名规范
- 实体类命名：统一以 `DomainEvent` 结尾
- 事件名应体现业务行为，如：`UserCreatedDomainEvent`、`OrderCanceledDomainEvent`

### Event处理流程
1. 在client层定义领域事件实体类
2. 在domain层发布领域事件
3. 在application层处理本工程领域事件
4. 在adapter层处理外部领域事件

## 其他关键约束
- 接口变更**必须向下兼容**。
- 如无特殊情况**禁止xml SQL提交**，gmt_create、gmt_modified默认不设值，代码审查时严格按照规范。
- 严格遵循Java命名规范、包结构规范。
- 所有接口必须有详细注释，重要方法需补充Javadoc。
- 重要变更需同步更新技术文档。
- 严禁出现硬编码、魔法值。
- 单元测试必须覆盖核心业务逻辑。
- 严格遵循代码评审流程，禁止未评审代码合入主干。

## 核心业务流程大纲

### 1. 业务管理流程
```
用户请求 → Client层 → Application层 → Domain层 → Infrastructure层
    ↓
响应返回 ← Client层 ← Application层 ← Domain层 ← Infrastructure层
```

### 2. 事件驱动流程
```
外部事件 → Adapter层 → Domain层 → Infrastructure层
    ↓
消息发送 ← Domain层 ← Infrastructure层
```

### 3. 数据处理流程
```
数据同步请求 → Client层 → Application层 → Domain层 → Infrastructure层
    ↓
缓存更新 ← Domain层 ← Infrastructure层
```

## 技术栈依赖大纲

### 框架依赖
```
企业级框架
├── Dubbo (RPC框架)
├── MyBatis (ORM框架)
├── MQ (消息队列)
├── Config (Apollo配置中心)
├── Logging (日志框架)
```

### 核心依赖
```
项目依赖
├── mapstruct (对象转换)
├── lombok (代码生成)
├── fastjson (JSON处理)
├── commons-collections4 (集合工具)
├── commons-lang3 (工具类)
└── tk.mybatis (MyBatis扩展)
```

## 命名规范

### 模块命名
- **xxx-client**: 接口层模块
- **xxx-application**: 应用层模块
- **xxx-domain**: 领域层模块
- **xxx-infrastructure**: 基础设施层模块
- **xxx-adapter**: 适配层模块

### 包命名
- **client**: 对外接口和模型
- **application**: 应用服务和业务流程
- **domain**: 领域模型和业务逻辑
- **infrastructure**: 基础设施和技术实现
- **adapter**: 外部系统适配

### 类命名
- **Service**: 服务接口和实现
- **Repository**: 仓储接口和实现
- **Manager**: 管理器接口和实现
- **Convertor**: 转换器
- **Handler**: 处理器
- **Entity**: 数据库实体
- **DTO**: 数据传输对象
- **VO**: 视图对象

## 补充
### 接口实现规范
1. 服务注册与网关配置：
   - 类级别：
     * 使用dubbo的`@Service`注解注册为RPC服务
     * 使用`@ZsmpService`注解声明服务名称
     * 示例：
       ```java
       @ZsmpService(name = "文档")
       @Service
       public class DocumentServiceImpl implements DocumentService {
           // 实现方法
       }
       ```
   - 方法级别：
     * 使用`@ZsmpApiOperation`注解声明网关接口，包含以下配置：
       ```java
       @ZsmpApiOperation(
           description = "接口描述",
           gatewayApi = @GatewayApi(
               namespace = "xxx",           // 网关命名空间
               name = "xxx/xxx",              // 接口路径
               description = "接口描述",       // 接口描述
               webAuth = @GatewayWebAuth(     // 认证配置
                   needSession = true,        // 是否需要会话
                   needUserInfoContext = true // 是否需要用户信息
               )
           )
       )
       ```

2. 实现位置：
   - 网关接口的实现**统一在application层**。
   - 实现类命名建议以ServiceImpl结尾，如：`UserServiceImpl`、`OrderServiceImpl`。

3. 实现流程：
   - 在application层接收请求参数。
   - 转换为domain层的command或query对象。
   - 调用domain层的服务处理业务逻辑。
   - 返回处理结果。

## 网关接口规范
### 接口定义规范
1. 接口位置：
   - 所有对外暴露的网关接口**统一在client层的service目录下定义**。
   - 接口命名建议以Service结尾，如：`UserService`、`OrderService`。

2. 入参规范：
   - 接口方法的入参**必须声明为xxxReq对象**。
   - Req对象统一在client层的model/req目录下定义。
   - 示例：`UserCreateReq`、`OrderQueryReq`。

3. 出参规范：
   - 接口方法的出参建议使用统一的响应对象。
   - 复杂对象建议定义Resp对象，放在client层的model/resp目录下。


## 代码质量规范
### 方法规范
1. 方法长度：
   - 单个方法的代码行数**尽量控制在80行以内**。
   - 如果超过80行，需要考虑方法拆分。
   - 行数统计不包括注释和空行。

2. 方法职责：
   - 一个方法只做一件事。
   - 保持方法的原子性和单一职责。
   - 避免过多的参数传递（建议不超过5个）。

### 性能规范
1. IO操作：
   - **严禁在循环中进行IO操作**，包括但不限于：
     * 数据库操作（增删改查）
     * 远程调用（RPC、HTTP等）
     * 文件操作
     * Redis操作
   - 对于批量数据，应使用批量接口进行处理。
   - 必要时可使用并行流或线程池优化性能。

2. 批量处理：
   - 使用批量接口时，建议单批次数据量不超过1000条。
   - 大量数据处理时，采用分页或分批策略。
   - 注意控制并发度，避免资源耗尽。


这个大纲为微服务项目提供了通用的架构参考，涵盖了从接口层到基础设施层的所有核心组件和依赖关系，可作为其他工程进行项目组织的标准方案。 